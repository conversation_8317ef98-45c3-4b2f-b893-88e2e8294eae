package com.subfg.subfgapi.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.subfg.domain.request.BindThirdPartyAccountReq;
import com.subfg.domain.request.ChangeEmailReq;
import com.subfg.domain.request.UnbindThirdPartyAccountReq;
import com.subfg.domain.request.UpdateUserInfoReq;
import com.subfg.domain.vo.Result;
import com.subfg.subfgapi.Serivce.UserService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/user")
@Tag(name = "用户管理", description = "用户管理相关接口")
public class UserController {

    private final UserService userService;

    /**
     * 用户修改个人信息
     */
    @PostMapping("/updateUserInfo")
    @Operation(summary = "用户修改个人信息", description = "用户修改个人信息")
    public Result<String> updateUserInfo(@Valid @RequestBody UpdateUserInfoReq req){
        userService.updateUserInfo(req);
        return Result.successI18n("user.updateUserInfo.success");
    }

    /**
     * 用户绑定三方账号 目前系统只支持微信
     */
    @PostMapping("/bindThirdPartyAccount")
    @Operation(summary = "用户绑定三方账号", description = "用户绑定三方账号")
    public Result<String> bindThirdPartyAccount(@Valid @RequestBody BindThirdPartyAccountReq req){
        userService.bindThirdPartyAccount(req);
        return Result.successI18n("user.bindThirdPartyAccount.success");
    }

    /**
     * 用户解绑三方账号
     */
    @PostMapping("/unbindThirdPartyAccount")
    @Operation(summary = "用户解绑三方账号", description = "用户解绑三方账号")
    public Result<String> unbindThirdPartyAccount(@Valid @RequestBody UnbindThirdPartyAccountReq req){
        userService.unbindThirdPartyAccount(req);
        return Result.successI18n("user.unbindThirdPartyAccount.success");
    }

    /**
     * 用户换绑邮箱
     */
    @PostMapping("/changeEmail")
    @Operation(summary = "用户换绑邮箱", description = "用户换绑邮箱")
    public Result<String> changeEmail(@Valid @RequestBody ChangeEmailReq req){
        userService.changeEmail(req);
        return Result.successI18n("user.changeEmail.success");
    }
    
}
