# 通用消息
common.success=操作成功
common.error=操作失败
common.save.success=保存成功
common.save.error=保存失败
common.delete.success=删除成功
common.delete.error=删除失败
common.update.success=更新成功
common.update.error=更新失败
common.query.success=查询成功
common.query.error=查询失败
common.not.found=数据不存在
common.already.exists=数据已存在
common.invalid.parameter=参数无效
common.access.denied=访问被拒绝
common.system.error=系统错误

# 用户相关
user.login.success=登录成功
user.login.error=登录失败
user.logout.success=退出成功
user.register.success=注册成功
user.register.error=注册失败
user.password.error=密码错误
user.not.found=用户不存在
user.not.exists=用户不存在
user.already.exists=用户已存在
user.disabled=用户已被禁用
user.email.invalid=邮箱格式无效
user.password.weak=密码强度不够
user.updateUserInfo.success=个人信息修改成功
user.update.failed=用户信息更新失败
user.email.already.exists=邮箱已被其他用户使用
user.phone.already.exists=手机号已被其他用户使用
user.username.already.exists=用户名已被其他用户使用
user.bindThirdPartyAccount.success=三方账号绑定成功
user.bind.platform.not.supported=不支持的第三方平台类型
user.bind.wechat.already.bound=该微信账号已被其他用户绑定
user.bind.wechat.already.bound.current=您已绑定微信账号，请先解绑后再重新绑定
user.bind.failed=绑定失败，请稍后重试
user.unbindThirdPartyAccount.success=三方账号解绑成功
user.unbind.platform.not.supported=不支持的第三方平台类型
user.unbind.wechat.not.bound=您尚未绑定微信账号
user.unbind.failed=解绑失败，请稍后重试
user.changeEmail.success=邮箱换绑成功
user.new.email.code.error=新邮箱验证码错误或已过期
user.change.email.failed=邮箱换绑失败，请稍后重试

# 认证相关
auth.sendEmailCode.success=验证码发送成功
email.code.send.too.frequent=验证码发送过于频繁，请稍后再试
email.send.failed=邮件发送失败，请稍后重试
email.code.type.invalid=验证码类型无效

# 家庭组相关
family.group.create.success=家庭组创建成功
family.group.create.error=家庭组创建失败
family.group.join.success=加入家庭组成功
family.group.join.error=加入家庭组失败
family.group.leave.success=离开家庭组成功
family.group.leave.error=离开家庭组失败
family.group.not.found=家庭组不存在
family.group.full=家庭组已满
family.group.already.joined=已加入该家庭组
family.group.not.joined=未加入该家庭组
family.group.permission.denied=无权限操作该家庭组

# 产品相关
product.not.found=产品不存在
product.unavailable=产品不可用
product.price.changed=产品价格已变更

# 订单相关
order.create.success=订单创建成功
order.create.error=订单创建失败
order.pay.success=支付成功
order.pay.error=支付失败
order.cancel.success=订单取消成功
order.cancel.error=订单取消失败
order.not.found=订单不存在
order.status.invalid=订单状态无效

# 状态相关
status.active=活跃
status.inactive=非活跃
status.pending=待处理
status.approved=已批准
status.rejected=已拒绝
status.expired=已过期
status.cancelled=已取消

# 时间相关
time.today=今天
time.yesterday=昨天
time.tomorrow=明天
time.this.week=本周
time.this.month=本月
time.this.year=今年

# 验证消息
validation.required={0}不能为空
validation.email.invalid=邮箱格式无效
validation.phone.invalid=手机号格式无效
validation.length.min={0}长度不能少于{1}个字符
validation.length.max={0}长度不能超过{1}个字符
validation.length.range={0}长度必须在{1}到{2}个字符之间
validation.number.min={0}不能小于{1}
validation.number.max={0}不能大于{1}
validation.number.range={0}必须在{1}到{2}之间
validation.pattern.invalid={0}格式不正确
validation.date.invalid=日期格式无效
validation.future.date={0}必须是未来日期
validation.past.date={0}必须是过去日期

# 字段名称
field.username=用户名
field.password=密码
field.email=邮箱
field.phone=手机号
field.name=姓名
field.age=年龄
field.birthday=生日
field.address=地址
field.description=描述
field.title=标题
field.content=内容
field.amount=金额
field.quantity=数量
field.price=价格

# 错误消息
error.400=请求参数错误
error.401=未授权访问
error.403=禁止访问
error.404=请求的资源不存在
error.405=请求方法不支持
error.415=不支持的媒体类型：{0}
error.500=服务器内部错误
error.502=网关错误
error.503=服务不可用
error.504=网关超时

# 业务错误
error.business.user.not.login=用户未登录
error.business.user.permission.denied=用户权限不足
error.business.data.not.found=数据不存在
error.business.data.duplicate=数据重复
error.business.operation.failed=操作失败
error.business.parameter.invalid=参数无效
error.business.file.upload.failed=文件上传失败
error.business.file.download.failed=文件下载失败
error.business.network.timeout=网络超时
error.business.database.error=数据库错误
